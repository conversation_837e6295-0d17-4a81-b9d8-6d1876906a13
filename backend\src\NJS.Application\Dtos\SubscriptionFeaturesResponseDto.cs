namespace NJS.Application.DTOs
{
    public class SubscriptionFeaturesResponseDto
    {
        public List<SubscriptionPlanWithDetailsDto> Plans { get; set; } = new List<SubscriptionPlanWithDetailsDto>();
    }

    public class SubscriptionPlanWithDetailsDto
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string Slug => Name?.ToLowerInvariant().Replace(" ", "_");
        public bool IsActive { get; set; }
        public string StripePriceId { get; set; }
        
        // Legacy pricing fields (for backward compatibility)
        public decimal MonthlyPrice { get; set; }
        public decimal YearlyPrice { get; set; }
        public int MaxUsers { get; set; }
        public int MaxProjects { get; set; }
        public int MaxStorageGB { get; set; }
        
        // New structured data
        public List<FeatureDto> Features { get; set; } = new List<FeatureDto>();
        public List<PricingDto> Pricing { get; set; } = new List<PricingDto>();
        public LimitationsDto Limitations { get; set; }
    }
}
