using MediatR;
using Microsoft.Extensions.Logging;
using NJS.Application.CQRS.Subscriptions.Queries;
using NJS.Application.DTOs;
using NJS.Repositories.Interfaces;
using System.Threading;
using System.Threading.Tasks;

namespace NJS.Application.CQRS.Subscriptions.Handlers
{
    public class GetAllSubscriptionFeaturesQueryHandler : IRequestHandler<GetAllSubscriptionFeaturesQuery, SubscriptionFeaturesResponseDto>
    {
        private readonly ISubscriptionRepository _subscriptionRepository;
        private readonly ILogger<GetAllSubscriptionFeaturesQueryHandler> _logger;

        public GetAllSubscriptionFeaturesQueryHandler(
            ISubscriptionRepository subscriptionRepository,
            ILogger<GetAllSubscriptionFeaturesQueryHandler> logger)
        {
            _subscriptionRepository = subscriptionRepository;
            _logger = logger;
        }

        public async Task<SubscriptionFeaturesResponseDto> Handle(GetAllSubscriptionFeaturesQuery request, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Getting all subscription plans with features, pricing, and limitations");

            try
            {
                var subscriptionPlans = await _subscriptionRepository.GetAllSubscriptionPlansWithDetailsAsync();

                var response = new SubscriptionFeaturesResponseDto();

                foreach (var plan in subscriptionPlans)
                {
                    // Create pricing structure
                    var pricingStructure = new PricingStructureDto();

                    if (plan.Pricings != null)
                    {
                        foreach (var pricing in plan.Pricings)
                        {
                            switch (pricing.Type.ToLower())
                            {
                                case "monthly":
                                    pricingStructure.Monthly = new PricingDetailDto
                                    {
                                        Amount = pricing.Amount,
                                        Currency = pricing.Currency,
                                        Formatted = pricing.Formatted
                                    };
                                    break;
                                case "monthly_inr":
                                    pricingStructure.MonthlyInr = new PricingDetailDto
                                    {
                                        Amount = pricing.Amount,
                                        Currency = pricing.Currency,
                                        Formatted = pricing.Formatted
                                    };
                                    break;
                                case "onetime":
                                    pricingStructure.Onetime = new PricingDetailDto
                                    {
                                        Amount = pricing.Amount,
                                        Currency = pricing.Currency,
                                        Formatted = pricing.Formatted
                                    };
                                    break;
                                case "custom":
                                    pricingStructure.Custom = true;
                                    pricingStructure.Formatted = pricing.Formatted;
                                    pricingStructure.Currency = pricing.Currency;
                                    break;
                            }
                        }
                    }

                    // Map features to string array (simplified feature names)
                    var featureNames = new List<string>();
                    if (plan.SubscriptionPlanFeatures != null)
                    {
                        foreach (var spf in plan.SubscriptionPlanFeatures)
                        {
                            var featureName = MapFeatureToSlug(spf.Feature.Name);
                            if (!string.IsNullOrEmpty(featureName))
                            {
                                featureNames.Add(featureName);
                            }
                        }
                    }

                    var planDto = new SubscriptionPlanWithDetailsDto
                    {
                        Id = plan.StripePriceId ?? $"plan_{plan.Name.ToLower()}_{DateTime.Now.Year}",
                        Name = plan.Name,
                        Description = plan.Description,
                        Pricing = pricingStructure,
                        Features = featureNames,
                        Limitations = plan.Limitations != null ? new LimitationsStructureDto
                        {
                            UsersIncluded = plan.Limitations.UsersIncluded,
                            Projects = plan.Limitations.Projects,
                            StorageGb = plan.Limitations.StorageGB,
                            Support = plan.Limitations.Support
                        } : null
                    };

                    response.Plans.Add(planDto);
                }

                _logger.LogInformation("Successfully retrieved {Count} subscription plans with details", response.Plans.Count);
                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving subscription plans with details");
                throw;
            }
        }

        private string MapFeatureToSlug(string featureName)
        {
            return featureName?.ToLower() switch
            {
                "work breakdown structure (wbs)" => "wbs",
                "wbs version 2.0" => "wbs_v2",
                "gantt/timeline view" => "gantt_view",
                "odc (other direct cost) table" => "odc",
                "job start form" => "job_start_form",
                "estimated expenses table" => "estimated_expenses",
                "input register" => "input_register",
                "email notifications" => "email_notifications",
                "check & review logs" => "review_logs",
                "change control register" => "change_control",
                "monthly progress review" => "monthly_review",
                "quarterly progress review" => "quarterly_review",
                "weekly/daily progress review" => "weekly_review",
                "milestone tracking" => "milestone_tracking",
                "budget vs actual analysis" => "budget_analysis",
                "manpower planning" => "manpower_planning",
                "api integration" => "api_integration",
                "basic ui" => "user_experience_basic",
                "enhanced ux" => "user_experience_enhanced",
                "tailored ui/ux" => "user_experience_tailored",
                "basic export (pdf)" => "reporting_basic",
                _ => null
            };
        }
    }
}
