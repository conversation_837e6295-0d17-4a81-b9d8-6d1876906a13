using MediatR;
using Microsoft.Extensions.Logging;
using NJS.Application.CQRS.Subscriptions.Queries;
using NJS.Application.DTOs;
using NJS.Repositories.Interfaces;
using System.Threading;
using System.Threading.Tasks;

namespace NJS.Application.CQRS.Subscriptions.Handlers
{
    public class GetAllSubscriptionFeaturesQueryHandler : IRequestHandler<GetAllSubscriptionFeaturesQuery, SubscriptionFeaturesResponseDto>
    {
        private readonly ISubscriptionRepository _subscriptionRepository;
        private readonly ILogger<GetAllSubscriptionFeaturesQueryHandler> _logger;

        public GetAllSubscriptionFeaturesQueryHandler(
            ISubscriptionRepository subscriptionRepository,
            ILogger<GetAllSubscriptionFeaturesQueryHandler> logger)
        {
            _subscriptionRepository = subscriptionRepository;
            _logger = logger;
        }

        public async Task<SubscriptionFeaturesResponseDto> Handle(GetAllSubscriptionFeaturesQuery request, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Getting all subscription plans with features, pricing, and limitations");

            try
            {
                var subscriptionPlans = await _subscriptionRepository.GetAllSubscriptionPlansWithDetailsAsync();

                var response = new SubscriptionFeaturesResponseDto();

                foreach (var plan in subscriptionPlans)
                {
                    var planDto = new SubscriptionPlanWithDetailsDto
                    {
                        Id = plan.Id,
                        Name = plan.Name,
                        Description = plan.Description,
                        IsActive = plan.IsActive,
                        StripePriceId = plan.StripePriceId,
                        
                        // Legacy fields for backward compatibility
                        MonthlyPrice = plan.MonthlyPrice,
                        YearlyPrice = plan.YearlyPrice,
                        MaxUsers = plan.MaxUsers,
                        MaxProjects = plan.MaxProjects,
                        MaxStorageGB = plan.MaxStorageGB,

                        // Features
                        Features = plan.SubscriptionPlanFeatures?.Select(spf => new FeatureDto
                        {
                            Id = spf.Feature.Id,
                            Name = spf.Feature.Name,
                            Description = spf.Feature.Description,
                            PriceUSD = spf.Feature.PriceUSD,
                            PriceINR = spf.Feature.PriceINR
                        }).ToList() ?? new List<FeatureDto>(),

                        // Pricing
                        Pricing = plan.Pricings?.Select(p => new PricingDto
                        {
                            Id = p.Id,
                            Type = p.Type,
                            Currency = p.Currency,
                            Amount = p.Amount,
                            Formatted = p.Formatted
                        }).ToList() ?? new List<PricingDto>(),

                        // Limitations
                        Limitations = plan.Limitations != null ? new LimitationsDto
                        {
                            Id = plan.Limitations.Id,
                            UsersIncluded = plan.Limitations.UsersIncluded,
                            Projects = plan.Limitations.Projects,
                            StorageGB = plan.Limitations.StorageGB,
                            Support = plan.Limitations.Support
                        } : null
                    };

                    response.Plans.Add(planDto);
                }

                _logger.LogInformation("Successfully retrieved {Count} subscription plans with details", response.Plans.Count);
                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving subscription plans with details");
                throw;
            }
        }
    }
}
