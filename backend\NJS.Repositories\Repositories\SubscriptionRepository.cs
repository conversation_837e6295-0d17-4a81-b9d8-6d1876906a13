using Microsoft.EntityFrameworkCore;
using NJS.Domain.Database;
using NJS.Domain.Entities;
using NJS.Repositories.Interfaces;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace NJS.Repositories.Repositories
{
    public class SubscriptionRepository : ISubscriptionRepository
    {
        private readonly ProjectManagementContext _context;

        public SubscriptionRepository(ProjectManagementContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<SubscriptionPlan>> GetAllSubscriptionPlansWithDetailsAsync()
        {
            return await _context.SubscriptionPlans
                .Include(sp => sp.SubscriptionPlanFeatures)
                    .ThenInclude(spf => spf.Feature)
                .Include(sp => sp.Pricings)
                .Include(sp => sp.Limitations)
                .Where(sp => sp.IsActive)
                .ToListAsync();
        }
    }
}
