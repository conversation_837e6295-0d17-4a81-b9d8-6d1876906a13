using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NJS.Domain.Entities
{
    public class Limitations
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public int SubscriptionPlanId { get; set; }

        [ForeignKey("SubscriptionPlanId")]
        public virtual SubscriptionPlan SubscriptionPlan { get; set; }

        public int UsersIncluded { get; set; }

        public int ProjectsIncluded { get; set; }

        public int StorageGB { get; set; }

        [MaxLength(100)]
        public string SupportLevel { get; set; } // "basic", "priority", "premium"

        public bool ApiAccess { get; set; } = false;

        public bool CustomBranding { get; set; } = false;

        public bool AdvancedReporting { get; set; } = false;

        public bool WhiteLabel { get; set; } = false;

        public bool SSO { get; set; } = false;

        public int MaxIntegrations { get; set; } = 0;

        public bool BackupRetention { get; set; } = false;

        [MaxLength(500)]
        public string AdditionalLimitations { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime? UpdatedAt { get; set; }
    }
}
